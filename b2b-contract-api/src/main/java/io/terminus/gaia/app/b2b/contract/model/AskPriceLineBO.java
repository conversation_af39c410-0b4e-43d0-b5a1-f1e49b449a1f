package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.md.model.UnitBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.*;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LookupMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运营端-询价单
 * <AUTHOR>
 */
@Model(
        name = "运营端-询价单明细行", deleteStrategy = DeleteStrategy.Logical,
        config = @ModelConfig(
                enableExport = true)
)
@Data
@EqualsAndHashCode(callSuper = true)
public class AskPriceLineBO extends BaseModel<Long> {
    @Field(name = "需求池", desc = "需求池")
    @LinkMeta
    private RequestPurchaseBO requestPurchaseBO;

    @Field(name = "询价单", desc = "询价单")
    @LinkMeta
    private AskPriceBO askPriceBO;

    @Field(name = "报价轮次", desc = "报价轮次")
    private Integer round;

    @Field(name = "物料名称", desc = "物料名称")
    private String materialName;

    @Field(name = "标品", desc = "标品")
    @LinkMeta
    private SpuBO spu;

    @Field(name = "标品名称", desc = "标品名称")
    private String spuName;

    @Field(name = "标品编码", desc = "标品编码")
    private String spuCode;

    @Field(name = "规格型号", desc = "规格型号")
    private String thingSizeDesc;

    @Field(name = "数量", desc = "数量")
    private BigDecimal needNum;

    @Field(name = "计量单位id", desc = "计量单位id")
    @LinkMeta
    private UnitBO unit;

    @Field(name = "计量单位", desc = "计量单位")
    private String unitName;

    /**
     * 标品的含铜量A
     * 特点：每年不变，定值，小数位不限制，保留到有数据那位，建议导入
     */
    @Field(name = "含铜量（kg/米）", desc = "含铜量（kg/米）")
    private BigDecimal rawMaterialContent;

    /**
     * 铜基价（含税）X：来源于采购方提的需求->需求池->询价
     * 特点：每天变，14：00前订单取当日上海有色金属网1#电解铜均价，14:00后-第二天上午11:00及节假日不允许下单
     * 来源：采购方提的需求->需求池->询价
     */
    @Field(name = "铜基价（含税）（元/吨）", desc = "铜基价（含税）（元/吨）")
    private BigDecimal purCopperBasicPrice;//basePriceWithTax;

    /**
     * 延米铜价（含税）B（元/米） = 含铜量 * 铜基价 / 1000
     * 要求：小数限制2位，四舍五入
     */
    @Field(name = "延米铜价（含税）（元/米）", desc = "延米铜价（含税）（元/米）")
    private BigDecimal purCopperPrice;

    //用于展示该行对应的报价明细
    @Field(name = "该行对应的报价明细List", desc = "该行对应的报价明细List")
    @LookupMeta(linkField = AskSupplierPriceLineBO.askPriceLineBO_field)
    private List<AskSupplierPriceLineBO> askSupplierPriceLineBOList;

    /**
     * 用于在询价详情里，展示该行对应的报价明细
     *
     * key：供应商id
     * value：供应商报的含税单价
     * */
    @Field(name = "该行对应的报价明细Map", desc = "该行对应的报价明细Map")
    @Transient
    private Map<String, BigDecimal> supplierPurTaxPriceMap;

    /**
     *
     *
     * @param id id
     * @return AskPriceBO
     */
    public static AskPriceLineBO of(Long id) {
        if (id == null) {
            return null;
        }
        AskPriceLineBO askPriceLineBO = new AskPriceLineBO();
        askPriceLineBO.setId(id);
        return askPriceLineBO;
    }

}
