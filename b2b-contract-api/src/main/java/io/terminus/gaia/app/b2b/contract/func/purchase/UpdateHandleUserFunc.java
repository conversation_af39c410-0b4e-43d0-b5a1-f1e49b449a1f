package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @class_name: UpdateHandleUserFunc
 * @desc: 更新需求单handleUser字段功能
 * @date: 2025/1/17
 * @author: Chonor
 **/
@Function
public interface UpdateHandleUserFunc {
    
    /**
     * 更新handleUser字段（如果为空）
     * @param requestPurchaseBO 需求单
     */
    void execute(RequestPurchaseBO requestPurchaseBO);
} 