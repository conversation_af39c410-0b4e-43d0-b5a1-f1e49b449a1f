package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @time 2025/7/9 15:55
 */
@Function
public interface DownloadAskSupplierPriceFunc {

    StringResult execute(AskSupplierPriceBO req);

}
