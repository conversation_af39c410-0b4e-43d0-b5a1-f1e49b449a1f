import { Controller } from 'nusi-sdk'

export default class extends Controller {

    multiTabs1Columns = [
        {title: '材料名称', key: 'needLineName'},
        {title: '规格型号', key: 'thingSizeDesc'},
        {title: '数量', key: 'needNum'},
        {title: '计量单位', key: 'unit.unitName'},
        {title: '含铜量', key: 'purRawMaterialContent'},
        {title: '延米铜价', key: 'saleCopperPrice'},
        {title: '铜基价', key: 'purCopperBasicPrice'},
        {title: '辅材及其他价格', key: 'saleOtherCosts'},
        {title: '综合含税单价', key: 'saleTaxPrice'}
    ]

    multiTabs2Columns = [
        {title: '材料名称', key: 'needLineName'},
        {title: '规格型号', key: 'thingSizeDesc'},
        {title: '数量', key: 'needNum'},
        {title: '计量单位', key: 'unit.unitName'},
        {title: '综合含税单价', key: 'saleTaxPrice'}
    ]
}