<?xml version="1.0" encoding="UTF-8" ?>
<View title="询价单详情" forModel="b2b_contract_AskPriceBO" type="Detail" version="2" showBack="true"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Detail key="AskPrice" title="基本信息"
            model="b2b_contract_AskPriceBO"
            dataFunction="b2b_contract_QueryAskPriceDetailFunc"
            dataParams="{id:pageContext.record.id}"
            onDataLoaded="#{dataLoaded}"
    >
        <Fields>
            <Field name="id" show="false"/>
            <Field name="code" label="询价单编号"/>
            <Field name="name" label="询价单名称"/>
            <Field name="projectBO.siteName" label="项目名称"/>
            <Field name="departmentBO" label="项目所属组织"/>
            <Field name="categoryBO.categoryName" label="询价品类"/>
            <Field name="basePriceWithTax" label="铜基价" />
            <Field name="status" label="状态"/>
            <Field name="createdBy.nickname" label="创建人"/>
            <Field name="remark" label="备注"/>
            <Field name="currentRound" label="当前轮次" show="false"/>
            <Field name="askSupplierPriceBOList" label="供应商报价单列表" show="false"/>
            <Field name="entityBOList" label="供应商" show="false"/>
        </Fields>
        <Actions>
            <Action layout="Footer" type="Cancel" after="GoBack" label="返回"/>
            <Action layout="Footer" label="驳回报价" action="#{openRejectDialog}" show="#{['price_waiting_confirm'].includes(this.record.status)}" />
            <Action layout="Footer" label="确认报价" action="#{openConfirmDialog}" show="#{['price_waiting_confirm'].includes(this.record.status)}" type="Submit" />
        </Actions>
    </Detail>

    <!-- 报价单明细：根据轮次和询价单id，查询询价单明细和供应商报价单价 -->
    <Detail key="AskPriceDetail" title="报价单明细" model="b2b_contract_AskPriceBO" mute="#{true}">
    </Detail>
    <frontend_MultiTabs
            key="multiTabs"
            showSwitch="#{false}"
            defaultSwitchValue="#{false}"
            enableCheck="#{false}"
            funcKey="b2b_contract_QueryRoundAskPriceLineFunc"
            columns="#{columns}"
            extraParams="#{{askPriceBO: {id: getContainerByKey('AskPrice')?.data?.id}, round: '$tabKey'}}"
            tabs="#{tabs}"
    />




<!--    <Table title="询价单明细"-->
<!--           model="b2b_contract_AskPriceLineBO"-->
<!--           key="AskPriceLineBO"-->
<!--           dataFunction="b2b_contract_PagingAskPriceLineBOBuiltInFunc"-->
<!--           dataParams="{askPriceBO:{id:{type: 'Collection', values: [pageContext.record.id]}}}">-->
<!--        <Fields>-->
<!--            <Field name="materialName" label="材料名称"/>-->
<!--            <Field name="thingSizeDesc" label="规格型号"/>-->

<!--            <Field name="needNum" label="数量"/>-->
<!--            <Field name="unit.unitName" label="计量单位"/>-->

<!--            <Field name="spu.spuCode" label="关联标品编码"/>-->
<!--            <Field name="spu.name" label="关联标品名称"/>-->

<!--            <Field name="rawMaterialContent" label="含铜量（kg/米）"/>-->
<!--            <Field name="purCopperBasicPrice" label="铜基价（含税）（元/吨）"/>-->
<!--            <Field name="purCopperPrice" label="延米铜价（含税）（元/米）"/>-->

<!--            &lt;!&ndash; 每个供应商报的含税单价 todo &ndash;&gt;-->

<!--        </Fields>-->
<!--    </Table>-->



    <!-- 询价结果回执 -->
    <Table title="询价结果回执"
           model="b2b_contract_AskSupplierPriceReplyTO"
           lookupFrom="AskPrice.replyTOList"
           key="AskSupplierPriceReplyBO">
        <Fields>
            <Field name="round" label="报价轮次"/>
            <Field name="supplierNames" label="供应商名称"/>
            <Field name="rejectReason" label="驳回原因"/>
        </Fields>
    </Table>

</View>
