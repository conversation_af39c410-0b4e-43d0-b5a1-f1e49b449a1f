<?xml version="1.0" encoding="UTF-8" ?>
<View title="需求详情" forModel="b2b_contract_RequestPurchaseBO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Detail key="RequestPurchase" model="b2b_contract_RequestPurchaseBO" dataCondition="id = ?" dataParams="[pageContext.record.id]" >
        <Fields>
            <Field name="id" show="false"/>
            <Field name="code"/>
            <Field name="name"/>
            <Field label="项目名称" name="projectBO.siteName" />
            <Field label="项目所属组织" name="departmentBO.departmentName"/>
            <Field label="需求品类" name="categoryBO.categoryName"/>
            <Field label="品牌" name="brandBOList" />
            <Field label="铜基价" name="basicPriceBO.priceWithTax" />
            <Field label="备注" name="remark"/>
            <Field label="状态" name="status"/>
            <Field label="提报人" name="createdBy"/>
            <Field name="submitQuote" show="false"/>
            <Field name="winSupplierBO" show="false"/>
        </Fields>
        <Actions>
            <Action label="返回" action="Close" layout="Footer"/>
            <Action label="加入合同意向" action="" layout="Footer" show="#{this.data.winSupplierBO != null}"/>
        </Actions>
    </Detail>

    <!-- 提交报价前展示 -->
    <Table title="需求明细" model="b2b_contract_RequestPurchaseLineBO" key="RequestPurchaseLine" fuzzySearchable="#{false}"
           dataFunction="b2b_contract_QueryRequestPurchaseLineFunc" dataParams="{id:pageContext.record.id}"
           show="#{getContainerByKey('RequestPurchase').data.submitQuote === false}">
        <Fields>
            <Field name="needLineName" label="材料名称"/>
            <Field name="thingSizeDesc" label="规格型号"/>
            <Field name="needNum" label="数量"/>
            <Field name="unit" label="计量单位"/>
        </Fields>

<!--        <Actions>-->
<!--            <Action label="导入明细" action="" />-->
<!--        </Actions>-->
    </Table>

    <!-- 提交报价后，设置中标供应商前 -->
    <frontend_MultiTabs
            key="multiTabs1"
            showSwitch="#{true}"
            enableCheck="#{true}"
            funcKey="b2b_contract_QueryBrandRequestPurchaseLineFunc"
            columns="#{multiTabs1Columns}"
            extraParams="#{{requestPurchaseBO: {id: getContainerByKey('RequestPurchase')?.data?.id}, brandBO: {id: '$tabKey'}}}"
            tabs="#{getContainerByKey('RequestPurchase')?.data?.brandBOList?.map(el => ({title: el.brandName, key: el.id})) || []}"
            show="#{getContainerByKey('RequestPurchase').data.submitQuote === true &amp;&amp; getContainerByKey('RequestPurchase').data.winSupplierBO == null}"
    />

    <!-- 设置中标供应商后展示 -->
    <frontend_MultiTabs
            key="multiTabs2"
            showSwitch="#{true}"
            enableCheck="#{true}"
            funcKey="b2b_contract_QueryBrandRequestPurchaseLineFunc"
            columns="#{multiTabs2Columns}"
            extraParams="#{{requestPurchaseBO: {id: getContainerByKey('RequestPurchase')?.data?.id}, brandBO: {id: '$tabKey'}}}"
            tabs="#{[{title: getContainerByKey('RequestPurchase')?.data?.winSupplierBO?.brandName, key: getContainerByKey('RequestPurchase')?.data?.winSupplierBO?.id}]}"
            show="#{getContainerByKey('RequestPurchase').data.winSupplierBO != null}"
    />
</View>
