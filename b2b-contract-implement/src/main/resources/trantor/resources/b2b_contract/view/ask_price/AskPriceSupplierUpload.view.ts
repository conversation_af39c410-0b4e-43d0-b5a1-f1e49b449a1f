import { Controller,state,utils,Toast,triggerLogicFunction,showMessage } from 'nusi-sdk'

export default class extends Controller {


    onFieldChange = async ({record, fieldName}) => {
        console.log('record', record)
        if (fieldName==='file' && record?.file?.files.length>0){
            try {
                utils.openGlobalLoading()
                let result = await this.triggerLogicFunction('b2b_contract_ImportAskSupplierPriceFunc', {
                    file:record.file,
                    askSupplierPriceId:this.env.id,
                });
                console.log('result',result)
                this.getContainerByKey('spu').updateData(result)
            }catch (e) {
                showMessage({
                    level: "Weak",
                    message: e.message,
                    type: "Error"
                })
                return
            }finally {
                utils.closeGlobalLoading()
            }
        }
    }
}
