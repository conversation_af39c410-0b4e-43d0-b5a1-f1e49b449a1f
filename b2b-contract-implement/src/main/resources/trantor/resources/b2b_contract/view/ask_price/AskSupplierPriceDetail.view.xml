<?xml version="1.0" encoding="UTF-8" ?>
<View title="报价单详情" forModel="b2b_contract_AskSupplierPriceBO" type="Detail" version="2" showBack="true"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Detail key="AskSupplierPrice" title="基本信息"
            model="b2b_contract_AskSupplierPriceBO"
            dataCondition="id = ?"
            dataParams="[pageContext.record.id]" >
        <Fields>
            <Field name="id" show="false"/>
            <Field name="askPriceBO.code" label="询价单编号"/>
            <Field name="askPriceBO.name" label="询价单名称"/>

            <Field name="askPriceBO.projectName" label="项目名称"/>
            <Field name="askPriceBO.enterpriseName" label="项目所属组织"/>

            <Field name="askPriceBO.categoryName" label="询价品类"/>
            <Field name="askPriceBO.basePriceWithTax" label="铜基价" />
            <Field name="status" label="状态"/>
            <Field name="createdBy.nickname" label="提报人姓名"/>
<!--            <Field name="remark" label="备注" show="false"/>-->
            <Field name="currentRound" label="当前轮数" show="false"/>
        </Fields>
        <Actions>
            <Action label="返回" action="GoBack" layout="Footer"/>
        </Actions>
    </Detail>

    <Detail key="AskPriceDetail" title="报价单明细" model="b2b_contract_AskPriceBO" mute="#{true}">
    </Detail>
    <frontend_MultiTabs
            key="multiTabs"
            enableCheck="#{false}"
            funcKey="b2b_contract_QueryRoundAskSupplierPriceLineFunc"
            columns="#{columns}"
            extraParams="#{{askSupplierPriceBO: {id: getContainerByKey('AskSupplierPrice')?.data?.id},  round: '$tabKey'}}"
            tabs="#{getTabs()}"
    />

    <!-- 询价结果回执 -->
    <Table title="询价结果回执"
           model="b2b_contract_AskSupplierPriceReplyTO"
           lookupFrom="AskSupplierPrice.replyTOList"
           key="AskSupplierPriceReplyBO">
        <Fields>
            <Field name="round" label="报价轮次"/>
            <Field name="supplierNames" label="供应商名称"/>
            <Field name="rejectReason" label="驳回原因"/>
        </Fields>
    </Table>


</View>
