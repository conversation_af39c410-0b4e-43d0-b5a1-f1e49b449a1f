<?xml version="1.0" encoding="UTF-8" ?>
<View title="上传报价文件" forModel="b2b_contract_AskSupplierPriceImportTO" type="Detail" version="2" showBack="true"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="AskSupplierPrice" title="基本信息"
          model="b2b_contract_AskSupplierPriceImportTO"
          onFieldChange="#{onFieldChange}">
        >
        <Fields>
            <Field name="askSupplierPriceId" show="false" initValue="#{pageContext.record.id}"/>
            <Field name="file" label="上传报价文件">
                <Validations>
                    <Validation required="true" message="请上传附件"/>
                </Validations>
            </Field>
        </Fields>
    </Form>
</View>