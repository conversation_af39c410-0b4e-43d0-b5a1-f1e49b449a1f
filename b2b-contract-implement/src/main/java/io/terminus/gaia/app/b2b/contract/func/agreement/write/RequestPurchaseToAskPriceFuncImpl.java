package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.func.askprice.write.CreateAskPriceFunc;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskPriceTO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementBrandTO;
import io.terminus.gaia.item.model.price.BasicPriceBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.organization.tmodel.IdResult;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @class_name: RequestPurchaseToAskPriceFuncImpl
 * @desc: 协议外商品转询价
 * @date: 2025/6/27 : 16:16
 * @author: Chonor
 **/
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class RequestPurchaseToAskPriceFuncImpl implements RequestPurchaseToAskPriceFunc{

    private final CreateAskPriceFunc createAskPriceFunc;

    @Override
    public BooleanResult execute(RequestPurchaseBO requestPurchaseBO) {

        // 检查输入参数
        if (Objects.isNull(requestPurchaseBO)) {
            throw new BusinessException("需求单参数不能为空");
        }
        
        if (Objects.isNull(requestPurchaseBO.getId())) {
            throw new BusinessException("需求单ID不能为空");
        }

        final Long requestPurchaseId = requestPurchaseBO.getId();
        final RequestPurchaseBO purchaseBO = DS.findById(RequestPurchaseBO.class, requestPurchaseId,
                "*,basicPriceBO.*,categoryBO.*,projectBO.*,departmentBO.*");
        
        if (Objects.isNull(purchaseBO)) {
            throw new BusinessException("未找到对应的需求单记录");
        }

        List<RequestPurchaseLineBO> requestPurchaseLineBOList = DS.findAll(RequestPurchaseLineBO.class, 
                "*,brandBO.*,spu.*,supplier.*", 
                "requestPurchaseBO.id = ? AND inAgreement = ?", 
                requestPurchaseId, false);

        // 校验协议外明细是否为空
        if (CollectionUtils.isEmpty(requestPurchaseLineBOList)) {
            log.warn("需求单ID: {} 没有协议外商品，无需转询价", requestPurchaseId);
            throw new BusinessException("没有协议外商品，无需转询价");
        }

        log.info("需求单ID: {} 找到协议外明细 {} 条", requestPurchaseId, requestPurchaseLineBOList.size());

        // 校验是否都关联了标品
        List<String> unLinkedSpuMessages = new ArrayList<>();
        for (RequestPurchaseLineBO line : requestPurchaseLineBOList) {
            if (Objects.isNull(line.getSpu())) {
                String brandName = (line.getBrandBO() != null && StringUtils.isNotBlank(line.getBrandBO().getBrandName())) 
                        ? line.getBrandBO().getBrandName() : "未知品牌";
                String thingSizeDesc = StringUtils.isNotBlank(line.getThingSizeDesc()) 
                        ? line.getThingSizeDesc() : "未知规格";
                unLinkedSpuMessages.add(brandName + " " + thingSizeDesc + " 未关联标品");
            }
        }

        if (!unLinkedSpuMessages.isEmpty()) {
            String errorMessage = String.join("；", unLinkedSpuMessages);
            log.warn("需求单ID: {} 存在未关联标品的明细: {}", requestPurchaseId, errorMessage);
            throw new BusinessException(errorMessage);
        }

        log.info("需求单ID: {} 所有协议外明细都已关联标品，开始转询价", requestPurchaseId);

        AskPriceTO askPriceTO = new AskPriceTO();
        askPriceTO.setRequestPurchaseBO(purchaseBO);
        askPriceTO.setCategoryBO(purchaseBO.getCategoryBO());
        askPriceTO.setProjectBO(purchaseBO.getProjectBO());
        askPriceTO.setDepartmentBO(purchaseBO.getDepartmentBO());

        // 处理基础价格，防止空指针
        final BasicPriceBO basicPriceBO = purchaseBO.getBasicPriceBO();
        if (Objects.nonNull(basicPriceBO) && Objects.nonNull(basicPriceBO.getPriceWithTax()) 
                && Objects.nonNull(basicPriceBO.getPriceWithTax().getValue())) {
            askPriceTO.setBasePriceWithTax(basicPriceBO.getPriceWithTax().getValue());
        }

        // 按品牌分组，过滤掉品牌为空的数据
        final Map<BrandBO, List<RequestPurchaseLineBO>> listMap = requestPurchaseLineBOList.stream()
                .filter(line -> Objects.nonNull(line.getBrandBO()))
                .collect(Collectors.groupingBy(RequestPurchaseLineBO::getBrandBO));

        if (listMap.isEmpty()) {
            throw new BusinessException("所有协议外明细的品牌信息都为空，无法转询价");
        }

        // 校验同一品牌下是否有重复标品
        validateDuplicateSpuInSameBrand(listMap);

        List<AgreementBrandTO> agreementBrandTOList = new ArrayList<>(listMap.size());
        for (Map.Entry<BrandBO, List<RequestPurchaseLineBO>> entry : listMap.entrySet()) {
            BrandBO brandBO = entry.getKey();
            List<RequestPurchaseLineBO> requestPurchaseLineBOS = entry.getValue();
            
            if (CollectionUtils.isEmpty(requestPurchaseLineBOS)) {
                log.warn("品牌 {} 对应的明细列表为空，跳过", 
                        brandBO.getBrandName() != null ? brandBO.getBrandName() : "未知品牌");
                continue;
            }

            final RequestPurchaseLineBO requestPurchaseLineBO = requestPurchaseLineBOS.get(0);

            AgreementBrandTO agreementBrandTO = new AgreementBrandTO();
            agreementBrandTO.setBrandBO(brandBO);
            // supplier可能为空，这里不做强制要求
            agreementBrandTO.setYfCompanyBO(requestPurchaseLineBO.getSupplier());
            agreementBrandTO.setRequestPurchaseLineBOList(requestPurchaseLineBOS);
            agreementBrandTOList.add(agreementBrandTO);
        }

        if (agreementBrandTOList.isEmpty()) {
            throw new BusinessException("没有有效的品牌明细可以转询价");
        }

        askPriceTO.setAgreementBrandTOList(agreementBrandTOList);

        final AskPriceBO execute = createAskPriceFunc.execute(askPriceTO);
        
        if (Objects.isNull(execute) || Objects.isNull(execute.getId())) {
            throw new BusinessException("创建询价单失败");
        }

        log.info("需求单ID: {} 成功转为询价单，询价单ID: {}", requestPurchaseId, execute.getId());
        return BooleanResult.TRUE;
    }

    /**
     * 校验同一品牌下是否有重复标品
     */
    private void validateDuplicateSpuInSameBrand(Map<BrandBO, List<RequestPurchaseLineBO>> brandMap) {
        List<String> duplicateMessages = new ArrayList<>();
        
        for (Map.Entry<BrandBO, List<RequestPurchaseLineBO>> entry : brandMap.entrySet()) {
            BrandBO brandBO = entry.getKey();
            List<RequestPurchaseLineBO> lineList = entry.getValue();
            
            String brandName = (brandBO != null && StringUtils.isNotBlank(brandBO.getBrandName())) 
                    ? brandBO.getBrandName() : "未知品牌";
            
            // 按标品ID分组，检查是否有重复
            Map<Long, List<RequestPurchaseLineBO>> spuMap = lineList.stream()
                    .filter(line -> line.getSpu() != null && line.getSpu().getId() != null)
                    .collect(Collectors.groupingBy(line -> line.getSpu().getId()));
            
            // 检查每个标品是否有多条明细
            for (Map.Entry<Long, List<RequestPurchaseLineBO>> spuEntry : spuMap.entrySet()) {
                List<RequestPurchaseLineBO> spuLines = spuEntry.getValue();
                if (spuLines.size() > 1) {
                    // 收集重复的规格型号
                    List<String> thingSizeDescs = spuLines.stream()
                            .map(line -> StringUtils.isNotBlank(line.getThingSizeDesc()) 
                                    ? line.getThingSizeDesc() : "未知规格")
                            .collect(Collectors.toList());
                    
                    String spuName = spuLines.get(0).getSpu() != null && StringUtils.isNotBlank(spuLines.get(0).getSpu().getName())
                            ? spuLines.get(0).getSpu().getName() : "未知标品";
                    
                    String message = String.format("%s下的%s关联同一标品%s，请检查！", 
                            brandName, String.join("、", thingSizeDescs), spuName);
                    duplicateMessages.add(message);
                }
            }
        }
        
        if (!duplicateMessages.isEmpty()) {
            String errorMessage = String.join("；", duplicateMessages);
            log.warn("发现重复标品关联: {}", errorMessage);
            throw new BusinessException(errorMessage);
        }
    }
}
