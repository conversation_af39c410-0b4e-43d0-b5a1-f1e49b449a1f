package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceLineTemplateTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.upload.OSSClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/7/9 15:56
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class DownloadAskSupplierPriceFuncImpl implements DownloadAskSupplierPriceFunc {

    private final OSSClient ossClient;

    @Override
    public StringResult execute(AskSupplierPriceBO req) {
        validate(req);

        // 查询供应商报价单信息
        AskSupplierPriceBO supplierPriceBO = DS.findById(AskSupplierPriceBO.class, req.getId());
        Assert.notNull(supplierPriceBO, ExceptionUtil.create("供应商报价单不存在"));

        // 查询询价单
        AskPriceBO askPriceBO = DS.findById(AskPriceBO.class, supplierPriceBO.getAskPriceBO().getId());

        // 查询供应商报价单行信息，包含关联的询价单行、需求池行、品牌等信息
        List<AskSupplierPriceLineBO> supplierPriceLineBOS = DS.findAll(AskSupplierPriceLineBO.class,
                "*, askPriceLineBO.*,requestPurchaseLineBO.*, requestPurchaseLineBO.brandBO.*",
                "askSupplierPriceBO=? and round=?", supplierPriceBO.getId(), askPriceBO.getCurrentRound());

        if (supplierPriceLineBOS.isEmpty()) {
            log.warn("供应商报价单[{}]没有明细行数据", supplierPriceBO.getId());
        }

        // 转换为导出数据
        List<AskSupplierPriceLineTemplateTO> exportData = convertToExportData(supplierPriceLineBOS, askPriceBO);

        // 生成Excel文件并上传到OSS
        return generateExcelAndUpload(exportData, supplierPriceBO);
    }

    private void validate(AskSupplierPriceBO req) {
        Assert.notNull(req.getId(), ExceptionUtil.create("询价单id不能为空"));
    }

    /**
     * 转换为导出数据
     */
    private List<AskSupplierPriceLineTemplateTO> convertToExportData(List<AskSupplierPriceLineBO> supplierPriceLineBOS, AskPriceBO askPriceBO) {
        List<AskSupplierPriceLineTemplateTO> exportList = new ArrayList<>();

        for (AskSupplierPriceLineBO lineBO : supplierPriceLineBOS) {
            AskSupplierPriceLineTemplateTO exportTO = new AskSupplierPriceLineTemplateTO();

            // 设置ID，用于导入时更新
            exportTO.setId(lineBO.getId());

            // 获取询价单行信息
            AskPriceLineBO askPriceLineBO = lineBO.getAskPriceLineBO();
            if (askPriceLineBO != null) {
                // 物料名称
                exportTO.setMaterialName(askPriceLineBO.getMaterialName());

                // 分类 - 从询价单获取
                exportTO.setCategoryName(askPriceBO.getCategoryName());
                // 规格
                exportTO.setSpecification(askPriceLineBO.getThingSizeDesc());

                // 数量
                exportTO.setQuantity(askPriceLineBO.getNeedNum());

                // 单位
                exportTO.setUnitName(askPriceLineBO.getUnitName());

                // 含铜量
                exportTO.setCopperContent(askPriceLineBO.getRawMaterialContent());

                // 铜基价
                exportTO.setCopperBasePrice(askPriceLineBO.getPurCopperBasicPrice());

                // 延米铜价
                exportTO.setCopperPricePerMeter(askPriceLineBO.getPurCopperPrice());
            }

            // 含税单价（必填）- 从供应商报价行获取
            exportTO.setTaxInclusiveUnitPrice(lineBO.getPurTaxPrice());

            // 品牌信息需要从需求池明细行获取
            if (lineBO.getRequestPurchaseLineBO() != null && lineBO.getRequestPurchaseLineBO().getBrandBO() != null) {
                exportTO.setBrandName(lineBO.getRequestPurchaseLineBO().getBrandBO().getBrandName());
            }

            exportList.add(exportTO);
        }

        return exportList;
    }

    /**
     * 生成Excel文件并上传到OSS
     */
    private StringResult generateExcelAndUpload(List<AskSupplierPriceLineTemplateTO> exportData, AskSupplierPriceBO supplierPriceBO) {
        try {
            String fileName = "询价清单导出-" + System.currentTimeMillis() + ".xlsx";

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, AskSupplierPriceLineTemplateTO.class)
                    .sheet("询价清单")
                    .doWrite(exportData);

            // 上传到OSS
            ByteArrayInputStream inputStream = IoUtil.toStream(outputStream);
            ossClient.upload(fileName, inputStream);

            String url = ossClient.getUrlWithoutSignature(fileName);
            if (StrUtil.startWith(url, "http:")) {
                url = StrUtil.replace(url, "http:", "https:");
            }

            log.info("询价清单导出完成，文件地址: {}", url);
            return new StringResult(url);

        } catch (Exception e) {
            log.error("导出询价清单失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }
}
