package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceImportTO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceLineTemplateTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 导入询价清单实现
 *
 * <AUTHOR>
 * @time 2025/7/9 17:00
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ImportAskSupplierPriceFuncImpl implements ImportAskSupplierPriceFunc {

    @Override
    @DSTransaction
    public BooleanResult execute(AskSupplierPriceImportTO importTO) {
        log.info("开始导入询价清单，参数: {}", JSON.toJSONString(importTO));

        validate(importTO);

        // 查询供应商报价单信息
        AskSupplierPriceBO supplierPriceBO = DS.findById(AskSupplierPriceBO.class, importTO.getAskSupplierPriceId());
        Assert.notNull(supplierPriceBO, ExceptionUtil.create("供应商报价单不存在"));

        List<Attachment.File> files = Opt.ofNullable(importTO).map(AskSupplierPriceImportTO::getFile).map(Attachment::getFiles).orElse(Collections.emptyList());
        Attachment.File file = CollUtil.getFirst(files);
        // 下载并解析Excel文件
        List<AskSupplierPriceLineTemplateTO> importData = downloadAndParseExcel(file.getUrl());

        // 校验导入数据
        validateImportData(importData);

        // 通过ID直接更新含税单价
        updateTaxInclusiveUnitPriceById(importData);

        log.info("询价清单导入完成，供应商报价单ID: {}", importTO.getAskSupplierPriceId());
        return BooleanResult.TRUE;
    }

    private void validate(AskSupplierPriceImportTO importTO) {
        Assert.notNull(importTO, ExceptionUtil.create("导入参数不能为空"));
        Assert.notNull(importTO.getAskSupplierPriceId(), ExceptionUtil.create("供应商报价单ID不能为空"));
        Assert.notNull(importTO.getFile(), ExceptionUtil.create("文件URL不能为空"));
    }

    /**
     * 下载并解析Excel文件
     */
    private List<AskSupplierPriceLineTemplateTO> downloadAndParseExcel(String fileUrl) {
        try {
            log.info("开始下载文件: {}", fileUrl);


            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            HttpUtil.download(fileUrl, outputStream, true);

            // 解析Excel文件
            List<AskSupplierPriceLineTemplateTO> importData = EasyExcel.read(new ByteArrayInputStream(outputStream.toByteArray()))
                    .head(AskSupplierPriceLineTemplateTO.class)
                    .sheet()
                    .doReadSync();

            log.info("解析Excel文件完成，共{}条数据", importData.size());
            return importData;

        } catch (Exception e) {
            log.error("下载或解析Excel文件失败: {}", fileUrl, e);
            throw new BusinessException("下载或解析Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 校验导入数据
     */
    private void validateImportData(List<AskSupplierPriceLineTemplateTO> importData) {
        if (CollectionUtils.isEmpty(importData)) {
            throw new BusinessException("导入文件为空，请检查文件内容");
        }

        List<String> errors = new ArrayList<>();

        for (int i = 0; i < importData.size(); i++) {
            AskSupplierPriceLineTemplateTO item = importData.get(i);
            int rowNum = i + 2; // Excel行号从2开始（第1行是表头）

            // 校验含税单价必填
            if (item.getTaxInclusiveUnitPrice() == null) {
                errors.add(String.format("第%d行：含税单价不能为空", rowNum));
                continue;
            }

            // 校验含税单价大于等于0
            if (item.getTaxInclusiveUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
                errors.add(String.format("第%d行：含税单价必须大于等于0", rowNum));
            }

            // 保留两位小数
            item.setTaxInclusiveUnitPrice(item.getTaxInclusiveUnitPrice().setScale(2, RoundingMode.HALF_UP));
        }

        if (!errors.isEmpty()) {
            throw new BusinessException("数据校验失败：\n" + String.join("\n", errors));
        }
    }

    /**
     * 更新含税单价
     */
    private void updateTaxInclusiveUnitPriceById(List<AskSupplierPriceLineTemplateTO> importData) {

        List<String> updateErrors = new ArrayList<>();
        int updatedCount = 0;

        for (int i = 0; i < importData.size(); i++) {
            AskSupplierPriceLineTemplateTO importItem = importData.get(i);
            int rowNum = i + 2;

            // 检查ID是否存在
            if (importItem.getId() == null) {
                updateErrors.add(String.format("第%d行：ID不能为空", rowNum));
                continue;
            }

            // 检查含税单价是否存在
            if (importItem.getTaxInclusiveUnitPrice() == null) {
                updateErrors.add(String.format("第%d行：含税单价不能为空", rowNum));
                continue;
            }

            try {
                // 更新含税单价
                AskSupplierPriceLineBO updateLine = new AskSupplierPriceLineBO();
                updateLine.setId(importItem.getId());
                updateLine.setPurTaxPrice(importItem.getTaxInclusiveUnitPrice());

                DS.update(updateLine);
                updatedCount++;

                log.debug("更新询价单行[{}]含税单价为: {}", importItem.getId(), importItem.getTaxInclusiveUnitPrice());

            } catch (Exception e) {
                log.error("更新询价单行[{}]失败", importItem.getId(), e);
                updateErrors.add(String.format("第%d行：更新失败，ID[%d]可能不存在", rowNum, importItem.getId()));
            }
        }

        if (!updateErrors.isEmpty()) {
            log.warn("部分数据更新失败：\n{}", String.join("\n", updateErrors));
            throw new BusinessException("部分数据更新失败：\n" + String.join("\n", updateErrors));
        }

        log.info("成功更新{}条询价单行数据", updatedCount);
    }


}
