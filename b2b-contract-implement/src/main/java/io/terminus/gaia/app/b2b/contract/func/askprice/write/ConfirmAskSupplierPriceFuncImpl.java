package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.func.agreement.write.SetWinSupplierFunc;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceConfirmTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2025/7/9 10:59
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ConfirmAskSupplierPriceFuncImpl implements ConfirmAskSupplierPriceFunc {

    private final SetWinSupplierFunc setWinSupplierFunc;

    @Override
    @DSTransaction
    public BooleanResult execute(AskSupplierPriceConfirmTO req) {
        validate(req);

        // 更新询价单状态
        updateAskPrice(req);

        List<AskSupplierPriceBO> supplierPriceList =
                DS.findAll(AskSupplierPriceBO.class, "*", "askPriceBO=?", req.getAskPriceId());
        Assert.notEmpty(supplierPriceList, ExceptionUtil.create("未找到供应商报价信息"));

        // 更新供应商报价状态，并获取中标供应商
        AskSupplierPriceBO winSupplierPrice = updateSupplierPrices(supplierPriceList, req);

        // 同步中标供应商到请购单
        syncWinSupplier(winSupplierPrice);

        return BooleanResult.TRUE;
    }

    // 抽取方法：更新询价单状态
    private void updateAskPrice(AskSupplierPriceConfirmTO req) {
        AskPriceBO askPriceUpdate = new AskPriceBO();
        askPriceUpdate.setId(req.getAskPriceId());
        askPriceUpdate.setStatus(AskPriceStatusDict.FINISH);
        askPriceUpdate.setPayableSchemeTemplateBO(req.getPayableSchemeTemplateBO());
        DS.update(askPriceUpdate);
    }

    // 抽取方法：批量更新供应商报价状态
    private AskSupplierPriceBO updateSupplierPrices(List<AskSupplierPriceBO> supplierPriceList,
                                                    AskSupplierPriceConfirmTO req) {
        List<AskSupplierPriceBO> toUpdate = new ArrayList<>();
        AskSupplierPriceBO winSupplier = null;
        for (AskSupplierPriceBO supplierPrice : supplierPriceList) {
            AskSupplierPriceBO priceUpdate = new AskSupplierPriceBO();
            priceUpdate.setId(supplierPrice.getId());
            String status = AskSupplierPriceStatusDict.LOSE;
            if (Objects.equals(supplierPrice.getSupplierEntity().getId(), req.getEntityBO().getId())) {
                status = AskSupplierPriceStatusDict.WIN;
                winSupplier = supplierPrice;
            }
            priceUpdate.setStatus(status);
            toUpdate.add(priceUpdate);
        }
        DS.update(toUpdate);
        Assert.notNull(winSupplier, ExceptionUtil.create("中标供应商不存在"));
        return winSupplier;
    }

    // 抽取方法：同步中标供应商
    private void syncWinSupplier(AskSupplierPriceBO winSupplierPrice) {
        RequestPurchaseBO requestPurchaseUpdate = new RequestPurchaseBO();
        requestPurchaseUpdate.setId(winSupplierPrice.getRequestPurchaseBO().getId());
        requestPurchaseUpdate.setWinSupplierBO(winSupplierPrice.getSupplierEntity());
        setWinSupplierFunc.execute(requestPurchaseUpdate);
    }

    private void validate(AskSupplierPriceConfirmTO req) {
        Assert.notNull(req.getAskPriceId(), ExceptionUtil.create("询价单id不能为空"));
        Assert.notNull(req.getPayableSchemeTemplateBO(), ExceptionUtil.create("账期不能为空"));
        Assert.notNull(req.getEntityBO(), ExceptionUtil.create("中标供应商不能为空"));
    }
}
