package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.context.TContext;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;

/**
 * @class_name: UpdateHandleUserFuncImpl
 * @desc: 更新需求单handleUser字段功能实现
 * @date: 2025/1/17
 * @author: Chonor
 **/
@Slf4j
@FunctionImpl
public class UpdateHandleUserFuncImpl implements UpdateHandleUserFunc {

    @Override
    public void execute(RequestPurchaseBO requestPurchaseBO) {
        try {
            if (requestPurchaseBO != null && requestPurchaseBO.getId() != null) {
                // 重新查询获取最新的handleUser信息
                RequestPurchaseBO latestRequestPurchase = DS.findById(RequestPurchaseBO.class, requestPurchaseBO.getId(), "handleUser");
                if (latestRequestPurchase != null && latestRequestPurchase.getHandleUser() == null) {
                    RequestPurchaseBO update = new RequestPurchaseBO();
                    update.setId(requestPurchaseBO.getId());
                    update.setHandleUser(TContext.getCurrentUser()); // 设置当前用户
                    DS.update(update);
                    log.info("更新需求单handleUser字段，需求单ID: {}", requestPurchaseBO.getId());
                }
            }
        } catch (Exception e) {
            log.error("更新handleUser字段失败，需求单ID: {}, 错误: {}", 
                    requestPurchaseBO != null ? requestPurchaseBO.getId() : "null", e.getMessage(), e);
        }
    }
} 