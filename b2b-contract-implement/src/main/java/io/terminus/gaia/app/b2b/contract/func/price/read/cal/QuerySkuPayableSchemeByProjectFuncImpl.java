package io.terminus.gaia.app.b2b.contract.func.price.read.cal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SkuPayableSchemeReqTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SkuPayableSchemeRespTO;
import io.terminus.gaia.item.model.sku.OneMouthfulLinePriceBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@FunctionImpl
public class QuerySkuPayableSchemeByProjectFuncImpl implements QuerySkuPayableSchemeByProjectFunc {
    @Override
    public List<SkuPayableSchemeRespTO> execute(List<SkuPayableSchemeReqTO> skuPayableSchemeReqTOList) {
        // 初始化出参
        List<SkuPayableSchemeRespTO> skuPayableSchemeRespTOList = new ArrayList<>();
        // 参数校验
        if (CollUtil.isEmpty(skuPayableSchemeReqTOList)) {
            throw new BusinessException("入参不能为空");
        }
        SkuPayableSchemeRespTO result;
        for (SkuPayableSchemeReqTO skuPayableSchemeReqTO : skuPayableSchemeReqTOList) {
            if (StrUtil.isBlank(skuPayableSchemeReqTO.getSkuNo())
                    || ObjectUtil.isNull(skuPayableSchemeReqTO.getProjectId())) {
                throw new BusinessException("入参采购商品编码，项目id不能为空");
            }

            SkuBO skuBO = DS.findOne(SkuBO.class, "*", "skuCode = ?", skuPayableSchemeReqTO.getSkuNo());
            if (ObjectUtil.isNull(skuBO)) {
                throw new BusinessException("商品sku不存在");
            }

            ProjectBO projectBO = DS.findById(ProjectBO.class, skuPayableSchemeReqTO.getProjectId(), "*");
            if (ObjectUtil.isNull(projectBO)) {
                throw new BusinessException("项目不存在");
            }
            if (ObjectUtil.isNull(projectBO.getAreaCode())) {
                throw new BusinessException("项目地址为空");
            }
            DistrictBO districtBO = DS.findOne(DistrictBO.class, "*", "districtCode = ? ", projectBO.getAreaCode());
            if (ObjectUtil.isNull(districtBO)) {
                throw new BusinessException("项目地址不存在");
            }

            result = new SkuPayableSchemeRespTO();
            result.setSkuNo(skuPayableSchemeReqTO.getSkuNo());
            result.setProjectId(skuPayableSchemeReqTO.getProjectId());
            List<PayableSchemeTemplateBO> payableSchemeTemplateBOList = new ArrayList<>();
            List<String> districtIdList = StrUtil.split(districtBO.getIdPath(), "/");
            if (CollUtil.isNotEmpty(districtIdList)) {
                for (int i = districtIdList.size() -1; i >= 0; i--) {
                    List<SalePriceCalLineBO> salePriceCalLineBOList  = new ArrayList<>();
                    if (ObjectUtil.isNull(skuBO.getBrandBO())) {
                        salePriceCalLineBOList = DS.findAll(SalePriceCalLineBO.class, "*, payableSchemeTemplateBO.*", "status = 'ENABLED' and spu = ? and districtBO = ?", skuBO.getSpu().getId(), districtIdList.get(i));
                    } else {
                        salePriceCalLineBOList = DS.findAll(SalePriceCalLineBO.class, "*, payableSchemeTemplateBO.*", "status = 'ENABLED' and spu = ? and districtBO = ? and brandBO = ?", skuBO.getSpu().getId(), districtIdList.get(i), skuBO.getBrandBO().getId());
                    }
                    if (CollUtil.isNotEmpty(salePriceCalLineBOList)) {
                        salePriceCalLineBOList.forEach(salePriceCalLineBO -> {
                            // 给应收账期方案赋值测算id
                            salePriceCalLineBO.getPayableSchemeTemplateBO().setSalePriceCalLineId(salePriceCalLineBO.getId());
                            // 一口价信息
                            List<OneMouthfulLinePriceBO> oneMouthfulLinePriceBOList = DS.findAll(OneMouthfulLinePriceBO.class, "*", "sku = ? and districtBO = ? and payableSchemeTemplateId = ?", skuBO.getId(), salePriceCalLineBO.getDistrictBO().getId(), salePriceCalLineBO.getPayableSchemeTemplateBO().getId());
                            if (CollUtil.isNotEmpty(oneMouthfulLinePriceBOList)) {
                                salePriceCalLineBO.getPayableSchemeTemplateBO().setOneMouthPriceWithTax(oneMouthfulLinePriceBOList.get(0).getAclSalePriceWithTax());
                                salePriceCalLineBO.getPayableSchemeTemplateBO().setOneMouthPriceWithoutTax(oneMouthfulLinePriceBOList.get(0).getAclSalePriceWithoutTax());
                            }
                            payableSchemeTemplateBOList.add(salePriceCalLineBO.getPayableSchemeTemplateBO());
                        });
                        break;
                    }
                }
            }
            payableSchemeTemplateBOList.sort(Comparator.comparing(it-> ObjectUtil.defaultIfNull(it.getSortIndex(), Long.MAX_VALUE)));
            result.setPayableSchemeTemplateBOList(payableSchemeTemplateBOList);
            skuPayableSchemeRespTOList.add(result);
        }
        return skuPayableSchemeRespTOList;
    }
}
