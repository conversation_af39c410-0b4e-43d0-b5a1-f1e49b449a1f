package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.func.purchase.SaveSupplierPriceFunc;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商确认询价功能实现
 *
 * <AUTHOR>
 * @time 2025/7/9 09:42
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class SupplierConfirmAskPriceFuncImpl implements SupplierConfirmAskPriceFunc {

    private final SaveSupplierPriceFunc saveSupplierPriceFunc;

    @Override
    public BooleanResult execute(AskSupplierPriceBO req) {
        log.info("供应商确认询价，入参:{}", req);

        // 1. 参数校验
        validate(req);

        AskSupplierPriceBO askSupplierPriceBO = new AskSupplierPriceBO();
        askSupplierPriceBO.setId(req.getId());
        askSupplierPriceBO.setStatus(AskSupplierPriceStatusDict.DONE);
        DS.update(askSupplierPriceBO);

        // 判断是否全部完成
        AskSupplierPriceBO supplierPriceBO = DS.findById(AskSupplierPriceBO.class, req.getId());
        AskPriceBO askPriceBO = supplierPriceBO.getAskPriceBO();

        List<AskSupplierPriceBO> allAskSupplierPriceBOS = DS.findAll(AskSupplierPriceBO.class, "*", "askPriceBO=?", askPriceBO.getId());
        judgeAllConfirm(allAskSupplierPriceBOS, supplierPriceBO);

        // 同步需求单报价
        syncRequire(supplierPriceBO);

        return BooleanResult.TRUE;
    }

    private void syncRequire(AskSupplierPriceBO supplierPriceBO) {

        List<AskSupplierPriceLineBO> supplierPriceLineBOS = DS.findAll(AskSupplierPriceLineBO.class, "*", "askSupplierPriceBO=? and round=?", supplierPriceBO.getAskPriceBO().getId(), supplierPriceBO.getCurrentRound());

        List<RequestPurchaseLineBO> requestPurchaseLineBOS = supplierPriceLineBOS.stream().map(it -> {
            RequestPurchaseLineBO requestPurchaseLineBO = new RequestPurchaseLineBO();
            requestPurchaseLineBO.setId(it.getRequestPurchaseLineBO().getId());
            requestPurchaseLineBO.setPurTaxPrice(it.getPurTaxPrice());
            return requestPurchaseLineBO;
        }).collect(Collectors.toList());

        saveSupplierPriceFunc.execute(requestPurchaseLineBOS);

    }

    private void judgeAllConfirm(List<AskSupplierPriceBO> allAskSupplierPriceBOS, AskSupplierPriceBO supplierPriceBO) {

        boolean allDone = allAskSupplierPriceBOS.stream().anyMatch(it -> Objects.equals(it.getStatus(), AskSupplierPriceStatusDict.DONE));
        if (!allDone) {
            return;
        }

        // 全都报价完成，更新为已确认
        AskPriceBO update = new AskPriceBO();
        update.setId(supplierPriceBO.getAskPriceBO().getId());
        update.setStatus(AskPriceStatusDict.PRICE_WAITING_CONFIRM);
        DS.update(update);
    }

    private void validate(AskSupplierPriceBO req) {
        Assert.notNull(req.getId(), ExceptionUtil.create("询价单id不能为空"));
    }

}
