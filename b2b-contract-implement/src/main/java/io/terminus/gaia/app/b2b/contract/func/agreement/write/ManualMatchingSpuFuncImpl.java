package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.func.purchase.UpdateHandleUserFunc;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.context.TContext;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @class_name: ManualMatchingSpuFuncImpl
 * @desc:
 * @date: 2025/6/27 : 10:29
 * @author: Chonor
 **/
@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class ManualMatchingSpuFuncImpl implements ManualMatchingSpuFunc{
    
    private final UpdateHandleUserFunc updateHandleUserFunc;
    @Override
    public BooleanResult execute(RequestPurchaseLineBO requestPurchaseLineBO) {

        final Long id = requestPurchaseLineBO.getId();
        if(Objects.isNull(id)){
            throw new BusinessException("请选择要匹配的需求明细");
        }

        final SpuBO spu = requestPurchaseLineBO.getSpu();
        if(Objects.isNull(spu)){
            throw new BusinessException("请选择要匹配的标品");
        }

        final RequestPurchaseLineBO requestPurchaseLineBOFromDB = DS.findById(RequestPurchaseLineBO.class, id);
        log.info("requestPurchaseLineBOFromDB: {}", requestPurchaseLineBOFromDB);
        final SpuBO spuBOFromDB = DS.findById(SpuBO.class, spu.getId());

        Query query = TSQL.select(TSQL.field("*"))
                .from(RequestPurchaseLineBO.class)
                .where(TSQL.field("requestPurchaseBO").eq(requestPurchaseLineBOFromDB.getRequestPurchaseBO().getId()))
                .and(TSQL.field("id").notEqual(requestPurchaseLineBOFromDB.getId()))
                .and(TSQL.field("thingSizeDesc").eq(requestPurchaseLineBOFromDB.getThingSizeDesc()))
                .and(TSQL.field("spu").isNull());

        List<RequestPurchaseLineBO> requestPurchaseLineBOList = DS.findAll(query);
        if(CollectionUtils.isEmpty(requestPurchaseLineBOList)){
            requestPurchaseLineBOList = new ArrayList<>();
        }
        requestPurchaseLineBOList.add(requestPurchaseLineBOFromDB);
        for (RequestPurchaseLineBO purchaseLineBO : requestPurchaseLineBOList) {
            RequestPurchaseLineBO update = new RequestPurchaseLineBO();
            update.setId(purchaseLineBO.getId());
            update.setSpu(spuBOFromDB);

            // 采购含铜量
            final BigDecimal purRawMaterialContent = (spuBOFromDB != null && spuBOFromDB.getRawMaterialContent() != null) ?
                    spuBOFromDB.getRawMaterialContent() : BigDecimal.ZERO;
            update.setPurRawMaterialContent(purRawMaterialContent);

            // 采购铜基价
            final BigDecimal purCopperBasicPrice = purchaseLineBO.getPurCopperBasicPrice() != null ?
                    purchaseLineBO.getPurCopperBasicPrice() : BigDecimal.ZERO;

            // 采购延米铜价 = （含铜量*铜基价/1000）
            final BigDecimal purCopperPrice = purRawMaterialContent.multiply(purCopperBasicPrice).divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
            update.setPurCopperPrice(purCopperPrice);
            // 销售延米铜价 = 采购延米铜价
            update.setSaleCopperPrice(purCopperPrice);

            DS.update(update);
        }

        // 更新handleUser字段
        updateHandleUserFunc.execute(requestPurchaseLineBOFromDB.getRequestPurchaseBO());

        return BooleanResult.TRUE;
    }
}
