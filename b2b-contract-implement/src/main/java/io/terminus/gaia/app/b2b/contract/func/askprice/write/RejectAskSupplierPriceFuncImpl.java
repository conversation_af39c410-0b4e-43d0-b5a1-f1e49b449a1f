package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceReplyTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.util.ModelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2025/7/8 16:40
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class RejectAskSupplierPriceFuncImpl implements RejectAskSupplierPriceFunc {


    @Override
    @DSTransaction
    public BooleanResult execute(AskSupplierPriceReplyTO req) {
        validate(req);

        initValue(req);

        AskPriceBO askPriceBO = DS.findById(AskPriceBO.class, req.getId());
        req.setRound(askPriceBO.getCurrentRound());

        // 所有供应商所有轮数报价
        Query query = TSQL.select(TSQL.field("*")).from(AskSupplierPriceLineBO.class)
                .where(TSQL.field(AskSupplierPriceLineBO.askPriceBO_field).eq(askPriceBO.getId()))
                .and(TSQL.field(AskSupplierPriceLineBO.round_field).eq(askPriceBO.getCurrentRound()));
        List<AskSupplierPriceLineBO> askSupplierPriceLineBOList = DS.findAll(query);

        // 所有供应商
        Query querySupplier = TSQL.select(TSQL.field("*")).from(AskSupplierPriceBO.class)
                .where(TSQL.field(AskSupplierPriceBO.askPriceBO_field).eq(askPriceBO.getId()));
        List<AskSupplierPriceBO> askSupplierPriceBOList = DS.findAll(querySupplier);

        // 更新拒绝的供应商报价状态和拒绝原因
        updateRejectSupplierStatus(req, askSupplierPriceBOList);

        // 更新询价单拒绝原因
        updateAskPriceReason(req, askPriceBO);

        // 更新轮数
        updateRound(askPriceBO, askSupplierPriceBOList);

        // 新增新一轮报价信息
        createNewRoundPrice(askSupplierPriceLineBOList);

        return BooleanResult.TRUE;
    }

    private void initValue(AskSupplierPriceReplyTO req) {
        req.setId(req.getAskPriceId());
        List<EntityBO> entityBOList = req.getEntityBOList();
        String supplierNames = entityBOList.stream().map(EntityBO::getEntityName).collect(Collectors.joining(","));
        req.setSupplierNames(supplierNames);
    }

    private void updateAskPriceReason(AskSupplierPriceReplyTO req, AskPriceBO askPriceBO) {
        AskPriceBO askPriceNew = new AskPriceBO();
        askPriceNew.setId(askPriceBO.getId());

        List<AskSupplierPriceReplyTO> replyList = ObjectUtil.defaultIfNull(askPriceBO.getReplyTOList(), new ArrayList<>());
        replyList.add(req);
        askPriceNew.setReplyTOList(replyList);

        DS.update(askPriceNew);
    }

    private void createNewRoundPrice(List<AskSupplierPriceLineBO> askSupplierPriceLineBOList) {
        List<AskSupplierPriceLineBO> supplierPriceLineBOS = askSupplierPriceLineBOList.stream().map(it -> {
            AskSupplierPriceLineBO clone = ModelUtils.clone(it);
            clone.setId(DS.nextId(AskSupplierPriceLineBO.class));
            clone.setRound(it.getRound() + 1);
            return clone;
        }).collect(Collectors.toList());

        DS.create(supplierPriceLineBOS);
    }

    private void updateRound(AskPriceBO askPriceBO, List<AskSupplierPriceBO> askSupplierPriceBOList) {
        int updateRound = askPriceBO.getCurrentRound() + 1;

        AskPriceBO askPriceNew = new AskPriceBO();
        askPriceNew.setId(askPriceBO.getId());
        askPriceNew.setCurrentRound(updateRound);
        DS.update(askPriceNew);


        List<AskSupplierPriceBO> supplierPriceBOS = askSupplierPriceBOList.stream().map(it -> {
            AskSupplierPriceBO askSupplierPriceNew = new AskSupplierPriceBO();
            askSupplierPriceNew.setId(it.getId());
            askSupplierPriceNew.setCurrentRound(updateRound);
            return askSupplierPriceNew;
        }).collect(Collectors.toList());
        DS.update(supplierPriceBOS);
    }

    private void updateRejectSupplierStatus(AskSupplierPriceReplyTO req, List<AskSupplierPriceBO> askSupplierPriceBOList) {
        Set<Long> rejectSupplierIdSet = req.getEntityBOList().stream().map(RootModel::getId).collect(Collectors.toSet());

        List<AskSupplierPriceBO> supplierPriceBOSNEw = askSupplierPriceBOList.stream()
                .filter(it -> rejectSupplierIdSet.contains(it.getSupplierEntity().getId()))
                .map(it -> {
                    AskSupplierPriceBO askSupplierPriceNew = new AskSupplierPriceBO();
                    askSupplierPriceNew.setId(it.getId());
                    askSupplierPriceNew.setStatus(AskSupplierPriceStatusDict.PRICE_REJECT);

                    List<AskSupplierPriceReplyTO> replyList = ObjectUtil.defaultIfNull(it.getReplyTOList(), new ArrayList<>());
                    replyList.add(req);
                    askSupplierPriceNew.setReplyTOList(replyList);
                    return askSupplierPriceNew;
                }).collect(Collectors.toList());

        DS.update(supplierPriceBOSNEw);
    }

    private void validate(AskSupplierPriceReplyTO req) {
        Assert.notNull(req.getAskPriceId(), ExceptionUtil.create("询价单id不能为空"));
        Assert.notNull(req.getEntityBOList(), ExceptionUtil.create("供应商不能为空"));
        Assert.notNull(req.getRejectReason(), ExceptionUtil.create("原因不能为空"));

    }
}
