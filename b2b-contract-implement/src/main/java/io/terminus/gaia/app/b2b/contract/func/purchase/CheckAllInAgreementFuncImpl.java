package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.dict.RequestPurchaseStatusDict;
import io.terminus.gaia.app.b2b.contract.func.purchase.CheckAllInAgreementFunc;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 检查需求单是否所有明细都匹配到协议实现类
 * 
 * <AUTHOR>
 */
@FunctionImpl
@Slf4j
public class CheckAllInAgreementFuncImpl implements CheckAllInAgreementFunc {
    
    @Override
    public BooleanResult execute(RequestPurchaseBO requestPurchaseBO) {
        final Long requestPurchaseId = requestPurchaseBO.getId();
        if (Objects.isNull(requestPurchaseId)) {
            throw new BusinessException("需求单ID不能为空");
        }
        
        log.info("检查需求单是否所有明细都匹配到协议，需求单ID: {}", requestPurchaseId);
        
        // 1. 查询需求单下的所有明细
        List<RequestPurchaseLineBO> requestPurchaseLines = DS.findAll(RequestPurchaseLineBO.class, 
                "id,inAgreement", 
                "requestPurchaseBO.id = ?", 
                requestPurchaseId);
        
        if (CollectionUtils.isEmpty(requestPurchaseLines)) {
            log.warn("需求单ID: {} 没有找到需求明细", requestPurchaseId);
            return BooleanResult.FALSE;
        }
        
        // 2. 检查是否所有明细均为协议内(即无需转询价)
        boolean allInAgreement = true;
        for (RequestPurchaseLineBO line : requestPurchaseLines) {
            if (!Boolean.TRUE.equals(line.getInAgreement())) {
                allInAgreement = false;
                break;
            }
        }
        
        log.info("需求单ID: {} 下共有 {} 条明细，所有明细都匹配到协议: {}", 
                requestPurchaseId, requestPurchaseLines.size(), allInAgreement);
        
        // 3. 如果所有明细都匹配到协议，则更新RequestPurchaseBO的allInAgreement为true
        if (allInAgreement) {
            RequestPurchaseBO update = new RequestPurchaseBO();
            update.setId(requestPurchaseId);
            update.setAllInAgreement(true);
            update.setStatus(RequestPurchaseStatusDict.FINISH);
            DS.update(update);
            log.info("需求单ID: {} 所有明细都匹配到协议，已更新allInAgreement为true", requestPurchaseId);
        }else {
            // 所有明细均匹配到协议，则更新需求单为已完成
            boolean allMatchAgreement = true;
            for (RequestPurchaseLineBO line : requestPurchaseLines) {
                if (Objects.isNull(line.getAgreement())) {
                    allMatchAgreement = false;
                    break;
                }
            }

            if(allMatchAgreement){
                RequestPurchaseBO update = new RequestPurchaseBO();
                update.setId(requestPurchaseId);
                update.setStatus(RequestPurchaseStatusDict.FINISH);
                DS.update(update);
            }
        }

        return BooleanResult.TRUE;
    }
} 