package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementBrandTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 创建询价单
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class CreateAskPriceFuncImpl implements CreateAskPriceFunc {

    private final UserInfoContext userInfoContext;

    /**
     * 常量定义
     */
    private static final Integer FIRST_ROUND = 1;


    @Override
    @DSTransaction
    public AskPriceBO execute(AskPriceTO askPriceTO) {
        log.info("生成询价单CreateAskPriceFuncImpl，入参:{}", askPriceTO);

        // 步骤1: 创建运营端询价单
        AskPriceBO askPriceBO = createAskPrice(askPriceTO);

        // 步骤2: 创建询价单明细清单
        List<AskPriceLineBO> askPriceLineBOList = createAskPriceLines(askPriceTO, askPriceBO);

        // 步骤3: 创建供应商报价单和明细
        createSupplierPrices(askPriceTO, askPriceBO, askPriceLineBOList);

        // 步骤4: 创建品牌关联关系
        createBrandRelations(askPriceTO, askPriceBO);

        return askPriceBO;
    }

    /**
     * 创建询价单
     */
    private AskPriceBO createAskPrice(AskPriceTO askPriceTO) {
        AskPriceBO askPriceBO = new AskPriceBO();
        askPriceBO.setId(DS.nextId(AskPriceBO.class));
        askPriceBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());

        // 询价单名称：20250707ABC项目
        askPriceBO.setName(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + askPriceTO.getProjectBO().getSiteName());
        askPriceBO.setStatus(AskPriceStatusDict.PRICE_WRITING);

        // 设置分类信息
        askPriceBO.setCategoryBO(askPriceTO.getCategoryBO());
        askPriceBO.setCategoryName(askPriceTO.getCategoryBO().getCategoryName());

        // 设置项目信息
        askPriceBO.setProjectBO(askPriceTO.getProjectBO());
        askPriceBO.setProjectName(askPriceTO.getProjectBO().getSiteName());

        // 设置部门和企业信息
        askPriceBO.setDepartmentBO(askPriceTO.getDepartmentBO());
        askPriceBO.setEnterpriseName(askPriceTO.getEnterpriseName());

        // 设置铜基价
        askPriceBO.setBasePriceWithTax(askPriceTO.getBasePriceWithTax());

        // 设置品牌和供应商列表
        setBrandAndSupplierLists(askPriceTO, askPriceBO);

        askPriceBO.setCurrentRound(FIRST_ROUND);
        DS.create(askPriceBO);

        return askPriceBO;
    }

    /**
     * 设置品牌和供应商列表
     */
    private void setBrandAndSupplierLists(AskPriceTO askPriceTO, AskPriceBO askPriceBO) {
        List<AgreementBrandTO> agreementBrandTOList = askPriceTO.getAgreementBrandTOList();
        if (CollectionUtils.isNotEmpty(agreementBrandTOList)) {
            List<BrandBO> brandBOList = agreementBrandTOList.stream()
                    .map(AgreementBrandTO::getBrandBO)
                    .collect(Collectors.toList());

            List<EntityBO> entityBOList = agreementBrandTOList.stream()
                    .map(AgreementBrandTO::getYfCompanyBO)
                    .collect(Collectors.toList());

            askPriceBO.setBrandBOList(brandBOList);
            askPriceBO.setEntityBOList(entityBOList);
        }
    }

    /**
     * 创建询价单明细清单
     */
    private List<AskPriceLineBO> createAskPriceLines(AskPriceTO askPriceTO, AskPriceBO askPriceBO) {
        // 根据需求池id查询需求池清单明细，按spuId去重
        List<RequestPurchaseLineBO> requestPurchaseLineBOList = getUniqueRequestPurchaseLines(askPriceTO);

        List<AskPriceLineBO> askPriceLineBOList = requestPurchaseLineBOList.stream()
                .filter(Objects::nonNull)
                .map(requestPurchaseLineBO -> createAskPriceLine(askPriceTO, askPriceBO, requestPurchaseLineBO))
                .collect(Collectors.toList());

        DS.create(askPriceLineBOList);
        return askPriceLineBOList;
    }

    /**
     * 获取去重后的需求池清单（原因：需求池的多个品牌可能对应同一个spu）
     */
    private List<RequestPurchaseLineBO> getUniqueRequestPurchaseLines(AskPriceTO askPriceTO) {
        List<RequestPurchaseLineBO> requestPurchaseLineBOList = DS.findAll(
                RequestPurchaseLineBO.class,
                "*, spu.*,unit.*",
                "requestPurchaseBO = ?",
                askPriceTO.getRequestPurchaseBO().getId()
        );

        // 按spuId去重
        return requestPurchaseLineBOList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(item -> item.getSpu().getId()))),
                        ArrayList::new
                ));
    }

    /**
     * 创建单个询价单明细行
     */
    private AskPriceLineBO createAskPriceLine(AskPriceTO askPriceTO, AskPriceBO askPriceBO,
                                              RequestPurchaseLineBO requestPurchaseLineBO) {
        AskPriceLineBO askPriceLineBO = new AskPriceLineBO();
        askPriceLineBO.setId(DS.nextId(AskPriceLineBO.class));
        askPriceLineBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
        askPriceLineBO.setAskPriceBO(askPriceBO);
        askPriceLineBO.setRound(FIRST_ROUND);

        // 设置标品和物料信息
        askPriceLineBO.setMaterialName(requestPurchaseLineBO.getNeedLineName());
        askPriceLineBO.setThingSizeDesc(requestPurchaseLineBO.getThingSizeDesc());
        askPriceLineBO.setNeedNum(requestPurchaseLineBO.getNeedNum());
        askPriceLineBO.setUnit(requestPurchaseLineBO.getUnit());
        askPriceLineBO.setUnitName(requestPurchaseLineBO.getUnit().getUnitName());
        askPriceLineBO.setSpu(requestPurchaseLineBO.getSpu());
        askPriceLineBO.setSpuCode(requestPurchaseLineBO.getSpu().getSpuCode());
        askPriceLineBO.setSpuName(requestPurchaseLineBO.getSpu().getName());

        // 设置价格相关信息
        askPriceLineBO.setRawMaterialContent(requestPurchaseLineBO.getPurRawMaterialContent());
        askPriceLineBO.setPurCopperBasicPrice(requestPurchaseLineBO.getPurCopperBasicPrice());
        askPriceLineBO.setPurCopperPrice(requestPurchaseLineBO.getPurCopperPrice());

        return askPriceLineBO;
    }

    /**
     * 创建供应商报价单和明细
     */
    private void createSupplierPrices(AskPriceTO askPriceTO, AskPriceBO askPriceBO,
                                      List<AskPriceLineBO> askPriceLineBOList) {
        List<AgreementBrandTO> agreementBrandTOList = askPriceTO.getAgreementBrandTOList();
        if (CollectionUtils.isEmpty(agreementBrandTOList)) {
            return;
        }

        for (AgreementBrandTO agreementBrandTO : agreementBrandTOList) {
            // 创建供应商报价单
            AskSupplierPriceBO askSupplierPriceBO = createSupplierPrice(askPriceTO, askPriceBO, agreementBrandTO);

            // 创建供应商报价单明细
            createSupplierPriceLines(askPriceTO, askPriceBO, askPriceLineBOList,
                    agreementBrandTO, askSupplierPriceBO);
        }
    }

    /**
     * 创建供应商报价单
     */
    private AskSupplierPriceBO createSupplierPrice(AskPriceTO askPriceTO, AskPriceBO askPriceBO,
                                                   AgreementBrandTO agreementBrandTO) {
        AskSupplierPriceBO askSupplierPriceBO = new AskSupplierPriceBO();
        askSupplierPriceBO.setId(DS.nextId(AskSupplierPriceBO.class));
        askSupplierPriceBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
        askSupplierPriceBO.setAskPriceBO(askPriceBO);
        askSupplierPriceBO.setSupplierEntity(agreementBrandTO.getYfCompanyBO());
        askSupplierPriceBO.setStatus(AskSupplierPriceStatusDict.PRICE_DOING);
        askSupplierPriceBO.setCurrentRound(FIRST_ROUND);

        if (userInfoContext != null && userInfoContext.getUserInfo() != null) {
            askSupplierPriceBO.setCreatedBy(userInfoContext.getUserInfo().getUser());
        }

        DS.create(askSupplierPriceBO);
        return askSupplierPriceBO;
    }

    /**
     * 创建供应商报价单明细
     */
    private void createSupplierPriceLines(AskPriceTO askPriceTO, AskPriceBO askPriceBO,
                                          List<AskPriceLineBO> askPriceLineBOList,
                                          AgreementBrandTO agreementBrandTO,
                                          AskSupplierPriceBO askSupplierPriceBO) {
        // 获取需要供应商报价的需求明细清单
        List<RequestPurchaseLineBO> rpLineBOForSuppliers = Optional.ofNullable(agreementBrandTO)
                .map(AgreementBrandTO::getRequestPurchaseLineBOList)
                .orElse(Collections.emptyList());

        if (CollectionUtils.isEmpty(rpLineBOForSuppliers)) {
            return;
        }

        List<Long> rpLineBOIds = rpLineBOForSuppliers.stream()
                .filter(Objects::nonNull)
                .map(RootModel::getId)
                .collect(Collectors.toList());

        List<RequestPurchaseLineBO> requestPurchaseLineBOs = DS.findAll(
                RequestPurchaseLineBO.class, "*, unit.*", "id in (?)", rpLineBOIds);

        // 创建供应商报价清单
        List<AskSupplierPriceLineBO> askSupplierPriceLineBOs = requestPurchaseLineBOs.stream()
                .map(requestPurchaseLineBO -> createSupplierPriceLine(
                        askPriceTO, askPriceBO, askPriceLineBOList, agreementBrandTO,
                        askSupplierPriceBO, requestPurchaseLineBO))
                .collect(Collectors.toList());

        DS.create(askSupplierPriceLineBOs);
    }

    /**
     * 创建单个供应商报价明细行
     */
    private AskSupplierPriceLineBO createSupplierPriceLine(AskPriceTO askPriceTO, AskPriceBO askPriceBO,
                                                           List<AskPriceLineBO> askPriceLineBOList,
                                                           AgreementBrandTO agreementBrandTO,
                                                           AskSupplierPriceBO askSupplierPriceBO,
                                                           RequestPurchaseLineBO requestPurchaseLineBO) {
        AskSupplierPriceLineBO askSupplierPriceLineBO = new AskSupplierPriceLineBO();
        askSupplierPriceLineBO.setId(DS.nextId(AskSupplierPriceLineBO.class));
        askSupplierPriceLineBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
        askSupplierPriceLineBO.setRequestPurchaseLineBO(requestPurchaseLineBO);
        askSupplierPriceLineBO.setAskPriceBO(askPriceBO);

        // 根据需求明细单的标品，匹配运营端的询价清单行
        Optional<AskPriceLineBO> askPriceLineBOOptional = askPriceLineBOList.stream()
                .filter(item -> item.getSpu().getId().equals(requestPurchaseLineBO.getSpu().getId()))
                .findFirst();

        askPriceLineBOOptional.ifPresent(askSupplierPriceLineBO::setAskPriceLineBO);

        // 设置供应商报价单相关信息
        askSupplierPriceLineBO.setAskSupplierPriceBO(askSupplierPriceBO);
        askSupplierPriceLineBO.setSupplierEntity(agreementBrandTO.getYfCompanyBO());
        askSupplierPriceLineBO.setRound(FIRST_ROUND);

        // 设置价格信息
        if (requestPurchaseLineBO.getPurTaxPrice() != null) {
            askSupplierPriceLineBO.setPurTaxPrice(requestPurchaseLineBO.getPurTaxPrice());
        }
        askSupplierPriceLineBO.setLineAmountWithTax(BigDecimal.ZERO);

        return askSupplierPriceLineBO;
    }

    /**
     * 创建询价单供应商品牌关联关系
     */
    private void createBrandRelations(AskPriceTO askPriceTO, AskPriceBO askPriceBO) {
        List<AgreementBrandTO> agreementBrandTOList = askPriceTO.getAgreementBrandTOList();
        if (CollectionUtils.isEmpty(agreementBrandTOList)) {
            return;
        }

        List<AskPriceBrandRelBO> askPriceBrandRelBOS = agreementBrandTOList.stream()
                .map(agreementBrandTO -> {
                    AskPriceBrandRelBO askPriceBrandRelBO = new AskPriceBrandRelBO();
                    askPriceBrandRelBO.setId(DS.nextId(AskPriceBrandRelBO.class));
                    askPriceBrandRelBO.setAskPriceBO(askPriceBO);
                    askPriceBrandRelBO.setBrandBO(agreementBrandTO.getBrandBO());
                    askPriceBrandRelBO.setEntityBO(agreementBrandTO.getYfCompanyBO());
                    return askPriceBrandRelBO;
                })
                .collect(Collectors.toList());

        DS.create(askPriceBrandRelBOS);
    }
}
